from uuid import UUID

from fastapi import API<PERSON><PERSON><PERSON>, Body, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.api.validators import validate_input_size
from services.base.application.exceptions import (
    BadRequestException,
    DuplicateDocumentsFound,
    IncorrectOperationException,
)
from services.data_service.api.constants import DataServicePrefixes, EventEndpointRoutes
from services.data_service.api.mappers.eventv3_api_output_mapper import EventV3APIOutputMapper
from services.data_service.api.models.output.events.event_api_output_v3 import EventV3APIOutput
from services.data_service.api.models.request.event.delete_event_api_request_input import DeleteEventAPIRequestInput
from services.data_service.api.models.request.event.insert_event_api_request_input import InsertEventAPIRequestInput
from services.data_service.api.models.request.event.modify_event_assets_api_request_input import (
    ModifyEventAssetsAPIRequestInput,
)
from services.data_service.api.models.request.event.update_event_api_request_input import UpdateEventAPIRequestInput
from services.data_service.api.models.request.feed.event_feed_api_request_input import EventFeedAPIRequestInput
from services.data_service.api.models.response.feed.event_feed_api_response import EventFeedAPIResponse
from services.data_service.application.use_cases.event_feed.event_feed_input_boundary import EventFeedInputBoundary
from services.data_service.application.use_cases.event_feed.event_feed_use_case import EventFeedUseCase
from services.data_service.application.use_cases.events.delete_event_by_id_use_case import DeleteEventByIdUseCase
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.models.delete_event_input_boundary import (
    DeleteEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.insert_event_input_boundary import (
    InsertEventInputBoundary,
)
from services.data_service.application.use_cases.events.models.modify_event_assets_input_boundary import (
    ModifyEventAssetsInputBoundary,
)
from services.data_service.application.use_cases.events.models.update_event_input_boundary import (
    UpdateEventInputBoundary,
)
from services.data_service.application.use_cases.events.modify_event_assets_use_case import ModifyEventAssetsUseCase
from services.data_service.application.use_cases.events.update_event_use_case import UpdateEventUseCase
from services.data_service.v1.api.continuation_token_marshaller import ContinuationTokenMarshaller
from services.data_service.v1.api.event_api_output_mapper import EventAPIOutputMapper

event_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.EVENT}",
    tags=["event"],
    responses={404: {"description": "Not found"}},
    dependencies=[Depends(validate_input_size)],
)


@event_router.post(
    EventEndpointRoutes.FEED,
    response_model=EventFeedAPIResponse,
    summary="Get Event Feed",
    description="""
    Retrieve a paginated feed of user events sorted by timestamp in descending order.

    This endpoint provides a chronological feed of user events with pagination support.
    Events are returned in reverse chronological order (newest first) and can be filtered
    by data type and organization. The feed uses continuation tokens for efficient pagination.

    **Key Features:**
    - **Chronological Ordering**: Events sorted by timestamp (newest first)
    - **Pagination**: Efficient pagination using continuation tokens
    - **Filtering**: Filter by data type and organization
    - **Immutable Filters**: Initial filters remain consistent across paginated requests
    - **Flexible Limits**: Adjustable page size and date range for each request

    **Query Parameters:**
    - `limit`: Number of events per page (configurable)
    - `continuation_token`: Token for retrieving subsequent pages
    - `data_type`: Filter events by specific data types
    - `organization`: Filter events by data source organization
    - `range`: Date range filter for event timestamps

    **Pagination Behavior:**
    - Initial request establishes filter criteria (data_type, organization)
    - Subsequent requests with continuation_token maintain these filters
    - Limit and range parameters can be modified for each request
    """,
    response_description="Paginated list of events with continuation token for next page",
    responses={
        200: {
            "description": "Events retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "items": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning workout",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "exercise",
                            }
                        ],
                        "continuation_token": "eyJsYXN0X3RpbWVzdGFtcCI6IjIwMjQtMDEtMTVUMDg6MzA6MDBaIn0=",
                    }
                }
            },
        },
        400: {"description": "Invalid request parameters or malformed continuation token"},
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request parameters"},
    },
)
async def event_feed_endpoint(
    event_feed_use_case: EventFeedUseCase = Injected(EventFeedUseCase),
    user_uuid: UUID = Depends(get_current_uuid),
    input_boundary: EventFeedInputBoundary = Depends(EventFeedAPIRequestInput.to_input_boundary),
):
    try:
        result = await event_feed_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)
        return EventFeedAPIResponse(
            items=[EventAPIOutputMapper.map(document=e) for e in result.events],
            continuation_token=ContinuationTokenMarshaller.encode_event_feed_continuation_token(
                result.continuation_token
            ),
        )
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err


@event_router.post(
    EventEndpointRoutes.BASE,
    summary="Create Events",
    description="""
    Create new events in the system for tracking personal data and activities.

    This endpoint allows users to insert one or more events into their personal data collection.
    Events can be of various types including core events, notes, symptoms, emotions, medications,
    sleep records, and more. Each event must include a name, timestamp, and type-specific data.

    **Key Features:**
    - **Multiple Event Types**: Support for symptoms, medications, exercises, notes, and more
    - **Bulk Creation**: Create multiple events in a single request
    - **Template Integration**: Events can be created from predefined templates
    - **Asset Support**: Attach files, images, or other media to events
    - **Duplicate Detection**: Prevents creation of identical events
    - **Flexible Structure**: Type-specific fields for detailed event data

    **Supported Event Types:**
    - **Symptoms**: Track health symptoms with ratings, body parts, and categories
    - **Medications**: Record medication intake with dosage and timing
    - **Exercise**: Log physical activities with duration and intensity
    - **Notes**: General text-based observations and thoughts
    - **Sleep**: Track sleep patterns and quality
    - **Nutrition**: Record meals and dietary information
    - **Emotions**: Log emotional states and mood tracking

    **Request Body:**
    - `documents`: Array of event objects to insert (minimum 1 required)
      - `name`: Event name (1-200 characters, required)
      - `timestamp`: ISO 8601 datetime when the event occurred (required)
      - `type`: Event type (required)
      - Type-specific fields (category, rating, etc.)
      - `end_time`: For events with duration (optional)
      - `tags`: Array of string tags for categorization (optional)
      - `note`: Additional text description (optional)
      - `assets`: Array of file attachments (optional)
      - `template_id`: UUID of template used to create event (optional)
    """,
    response_description="Successfully created events with generated IDs and system properties",
    responses={
        200: {
            "description": "Events successfully created",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning headache",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "symptom",
                                "category": "headache",
                                "rating": 6,
                                "body_parts": ["head"],
                                "tags": ["morning", "stress"],
                                "system_properties": {
                                    "created_at": "2024-01-15T08:30:00Z",
                                    "updated_at": "2024-01-15T08:30:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate events or validation errors",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate": {
                            "summary": "Duplicate events",
                            "value": {"detail": "event duplicates found in the input payload"},
                        },
                        "validation": {
                            "summary": "Validation error",
                            "value": {"detail": "Invalid event data: timestamp is required"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        422: {"description": "Validation error in request body"},
    },
)
async def insert_event_endpoint(
    request_input: InsertEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: InsertEventUseCase = Injected(InsertEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=InsertEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(
    EventEndpointRoutes.BASE,
    summary="Update Events",
    description="""
    Update existing events with new information while preserving their identity and system properties.

    This endpoint allows users to modify one or more existing events in their personal data collection.
    Users can update event properties such as name, timestamp, ratings, categories, and other
    type-specific fields. The event ID and type must be provided to identify which events to update.

    **Key Features:**
    - **Bulk Updates**: Modify multiple events in a single request
    - **Partial Updates**: Only specified fields are updated, others remain unchanged
    - **Type Validation**: Event type must match existing event for security
    - **Duplicate Detection**: Prevents updates that would create duplicate events
    - **Atomic Operations**: All updates in a batch succeed or fail together

    **Request Body:**
    - `documents`: Array of event objects to update (minimum 1 required)
      - `id`: UUID of the existing event to update (required)
      - `name`: Updated event name (1-200 characters, required)
      - `timestamp`: Updated ISO 8601 datetime (required)
      - `type`: Event type (must match existing event type, required)
      - Type-specific fields to update (category, rating, etc.)
      - `end_time`: Updated end time for events with duration (optional)
      - `tags`: Updated array of string tags (optional)
      - `plan_extension`: Updated plan extension data (optional)
    """,
    response_description="Successfully updated events with modified properties",
    responses={
        200: {
            "description": "Events successfully updated",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Updated headache severity",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "symptom",
                                "category": "headache",
                                "rating": 8,
                                "tags": ["morning", "severe"],
                                "system_properties": {
                                    "created_at": "2024-01-15T08:00:00Z",
                                    "updated_at": "2024-01-15T09:00:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - duplicate events, validation errors, or operation not allowed",
            "content": {
                "application/json": {
                    "examples": {
                        "duplicate": {
                            "summary": "Duplicate events",
                            "value": {"detail": "event duplicates found in the input payload"},
                        },
                        "type_mismatch": {
                            "summary": "Type validation error",
                            "value": {"detail": "Event type does not match existing event"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "One or more events not found"},
        422: {"description": "Validation error in request body"},
    },
)
async def update_event_endpoint(
    request_input: UpdateEventAPIRequestInput = Body(...),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: UpdateEventUseCase = Injected(UpdateEventUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=UpdateEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.patch(
    EventEndpointRoutes.MODIFY_ASSETS,
    summary="Modify Event Assets",
    description="""
    Modify asset attachments for existing events by adding, removing, or updating file attachments.

    This endpoint allows users to manage file attachments (assets) associated with existing events.
    Assets can include images, audio recordings, videos, or other files that provide additional
    context or evidence for the event. Operations include adding new assets, removing existing ones,
    or updating asset metadata.

    **Key Features:**
    - **Asset Management**: Add, remove, or update file attachments on events
    - **Multiple Formats**: Support for images, audio, video, and document files
    - **Bulk Operations**: Modify assets for multiple events in a single request
    - **Type Validation**: Event type must match existing event for security
    - **Size Limits**: Individual assets limited to 10MB for performance

    **Supported Asset Types:**
    - **Images**: JPEG, PNG, GIF, WebP for photos and visual documentation
    - **Audio**: MP3, WAV, M4A, OGG for voice notes and audio recordings
    - **Video**: MP4, MOV, AVI, WebM for video documentation
    - **Documents**: PDF, TXT for text-based attachments

    **Asset Operations:**
    - **Add**: Upload new assets with base64-encoded data
    - **Remove**: Delete existing assets by asset ID
    - **Update**: Modify asset metadata and properties

    **Request Body:**
    - `documents`: Array of event asset modification objects (minimum 1 required)
      - `id`: UUID of the existing event to modify (required)
      - `type`: Event type (must match existing event type, required)
      - `assets`: Array of asset operations to perform
    """,
    response_description="Successfully updated events with modified asset references and metadata",
    responses={
        200: {
            "description": "Event assets successfully modified",
            "content": {
                "application/json": {
                    "example": {
                        "documents": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Morning headache",
                                "timestamp": "2024-01-15T08:30:00Z",
                                "type": "symptom",
                                "assets": [
                                    {
                                        "asset_id": "asset_123",
                                        "asset_type": "image",
                                        "filename": "headache_photo.jpg",
                                        "size": 1024000,
                                    }
                                ],
                                "system_properties": {
                                    "created_at": "2024-01-15T08:00:00Z",
                                    "updated_at": "2024-01-15T09:00:00Z",
                                },
                            }
                        ]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - invalid data, duplicate events, or unsupported asset format",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Duplicate events",
                            "value": {"detail": "event duplicates found in the input payload"},
                        },
                        {
                            "summary": "Unsupported asset format",
                            "value": {
                                "detail": "Asset format not supported. Supported formats: JPEG, PNG, GIF, WebP, MP3, WAV, M4A, OGG, MP4, MOV, AVI, WebM, PDF, TXT"
                            },
                        },
                    ]
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "Event not found or access denied"},
        413: {
            "description": "Asset file too large",
            "content": {"application/json": {"example": {"detail": "Asset file size exceeds maximum limit of 10MB"}}},
        },
        422: {"description": "Validation error in request body"},
    },
)
async def modify_event_assets_endpoint(
    request_input: ModifyEventAssetsAPIRequestInput = Body(
        ...,
        description="Event asset modification request containing event IDs and asset operations",
        example={
            "documents": [
                {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "type": "symptom",
                    "assets": [
                        {
                            "asset_type": "image",
                            "data": "base64-encoded-image-data...",
                            "filename": "headache_photo.jpg",
                        }
                    ],
                }
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: ModifyEventAssetsUseCase = Injected(ModifyEventAssetsUseCase),
) -> CommonDocumentsResponse[EventV3APIOutput]:
    try:
        events = await use_case.execute_async(
            boundary=ModifyEventAssetsInputBoundary.map(model=request_input), owner_id=owner_id
        )
        documents_api_output = [EventV3APIOutputMapper.map(event) for event in events]
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except DuplicateDocumentsFound as err:
        raise BadRequestException(message="event duplicates found in the input payload") from err

    return CommonDocumentsResponse[EventV3APIOutput](documents=documents_api_output)


@event_router.delete(
    EventEndpointRoutes.BASE,
    summary="Delete Events",
    description="""
    Delete existing events from the system permanently.

    This endpoint allows users to permanently remove one or more events from their personal
    data collection. Once deleted, events cannot be recovered. Users must provide the event
    ID and type to identify which events to delete for security verification.

    **Key Features:**
    - **Permanent Deletion**: Events are completely removed and cannot be recovered
    - **Bulk Operations**: Delete multiple events in a single request
    - **Type Verification**: Event type must match existing event for security
    - **Asset Cleanup**: Associated files and assets are automatically deleted
    - **Ownership Protection**: Users can only delete their own events

    **Security Measures:**
    - Event type verification prevents accidental deletions
    - User ownership validation ensures data isolation
    - Atomic operations ensure all deletions succeed or fail together

    **Request Body:**
    - `documents`: Array of event deletion objects (minimum 1 required)
      - `id`: UUID of the existing event to delete (required)
      - `type`: Event type (must match existing event type for verification, required)

    **Important Considerations:**
    - **Irreversible**: Deletion is permanent and cannot be undone
    - **Asset Cleanup**: All associated files and media will be deleted
    - **Group Events**: Events that are part of a group may have deletion restrictions
    - **Data Integrity**: Related data references may be affected
    """,
    response_description="Successfully deleted event IDs",
    responses={
        200: {
            "description": "Events successfully deleted",
            "content": {
                "application/json": {
                    "example": {
                        "document_ids": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-************"]
                    }
                }
            },
        },
        400: {
            "description": "Bad request - invalid data or operation not allowed",
            "content": {
                "application/json": {
                    "examples": {
                        "type_mismatch": {
                            "summary": "Event type mismatch",
                            "value": {"detail": "Event type does not match existing event"},
                        },
                        "operation_not_allowed": {
                            "summary": "Operation not allowed",
                            "value": {"detail": "Cannot delete event: event is part of a protected group"},
                        },
                    }
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "One or more events not found or access denied"},
        422: {"description": "Validation error in request body"},
    },
)
async def delete_event_endpoint(
    request_input: DeleteEventAPIRequestInput = Body(
        ...,
        description="Event deletion request containing event IDs and types for verification",
        example={
            "documents": [
                {"id": "123e4567-e89b-12d3-a456-************", "type": "symptom"},
                {"id": "456e7890-e89b-12d3-a456-************", "type": "note"},
            ]
        },
    ),
    owner_id: UUID = Depends(get_current_uuid),
    use_case: DeleteEventByIdUseCase = Injected(DeleteEventByIdUseCase),
) -> CommonDocumentsIdsResponse:
    try:
        deleted_uuids = await use_case.execute_async(
            boundary=DeleteEventInputBoundary.map(model=request_input), owner_id=owner_id
        )
        return CommonDocumentsIdsResponse(document_ids=deleted_uuids)
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
